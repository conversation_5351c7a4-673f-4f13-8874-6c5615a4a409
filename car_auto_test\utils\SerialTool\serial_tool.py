import json
import time
import serial
import serial.tools.list_ports
from utils.LoggingSystem.Logger import logger
from config.Config import *



def find_usable_port(baudrate=9600, timeout=3.0):
    """
    自动检测可用串口并返回第一个可用的连接对象
    """
    ports = serial.tools.list_ports.comports()
    logger.info(f"ports: {ports}")

    for port_info in ports:
        logger.info(f"port: {port_info}")
        port = None
        try:
            # 尝试建立连接
            port = serial.Serial(
                port=port_info.device,
                baudrate=baudrate,
                timeout=timeout
            )

            # 发送测试命令
            test_command = 'ls\r\n'.encode('utf-8')  # 使用utf-8格式
            port.write(test_command)
            time.sleep(1)

            # 检查响应（至少需要等待数据到达）
            response = port.read(500)
            logger.info(f"response: {response}")
            resp_info = str(response).lower()

            # 如果有响应则认为可用
            if 'ls' in resp_info or 'cannot execute' in resp_info:
                logger.info(f"发现可用串口: {port_info.device}")
                return port
            else:
                port.close()
                return None
        except (serial.SerialException, OSError) as e:
            logger.warning(f"测试 {port_info.device} 失败: {str(e)}")
        finally:
            # 关闭不可用端口的连接
            if port and not port.is_open:
                port.close()

    return None


def get_serial_conn():
    for rate in define_rate:
        # 自动检测可用波特率端口
        valid_serial = find_usable_port(baudrate=rate, timeout=0.5)
        if valid_serial:
            logger.info(f"成功连接可用串口: {valid_serial.port}")
            return valid_serial
        time.sleep(1)
    return None


class SerialTool:
    def __init__(self):
        self.serial_tool = None
        self.port = get_serial_conn()
        if self.port is None:
            logger.error('未获取到串口连接，请检查串口是否正确连接或者重新拔插一下！')
            # raise Exception("未获取到串口连接，请检查串口是否正确连接或者重新拔插一下！")

    def base_action(self):
        into_path = ' '.join(['cd', vehicle_path, '\r\n'])
        self.port.write(into_path.encode('utf-8'))
        time.sleep(1)
        self.port.write((setup_cmd+'\r\n').encode('utf-8'))

    def power_on(self):
        self.base_action()
        cmd = './vehicle_info --remote-power-on\n'
        logger.info('远程上电')
        self.port.write(cmd.encode('utf-8'))
        time.sleep(3)

    def power_off(self):
        self.base_action()
        logger.info('远程下电')
        cmd = './vehicle_info --remote-power-off\n'
        self.port.write(cmd.encode('utf-8'))

    def ota_in(self):
        self.base_action()
        logger.info('进入ota模式')
        cmd = './vehicle_info --ota-mode-in\n'
        self.port.write(cmd.encode('utf-8'))

    def ota_out(self):
        self.base_action()
        logger.info('退出ota模式')
        cmd = './vehicle_info --ota-mode-out\n'
        self.port.write(cmd.encode('utf-8'))

    def rm_package(self):
        logger.info('删除升级包')
        cmd = 'rm /var/update/ftp/*\n'
        self.port.write(cmd.encode('utf-8'))


    def set_file(self, *args):
        """
        设置配置文件，支持传递多个键值对
        """
        from config.Config import eea4_qnx_config_file
        c_json = eea4_qnx_config_file.copy()

        # 处理参数
        if len(args) == 2:
            # 单个键值对
            key, value = args
            logger.info(f"设置单个键值对: {key}={value}")
            c_json[key] = value
        elif len(args) > 2 and len(args) % 2 == 0:
            # 多个键值对
            for i in range(0, len(args), 2):
                key = args[i]
                value = args[i+1]
                logger.info(f"设置键值对: {key}={value}")
                c_json[key] = value
        else:
            logger.error("参数格式错误，应为(key, value)或(key1, value1, key2, value2, ...)")
            return False

        logger.info(f"更新后的配置: {c_json}")
        config_file_json = json.dumps(c_json)
        try:
            if self.port.isOpen():
                self.port.write('mount -uw /\n'.encode('utf-8'))
                # self.port.write(b'echo \'' + config_file_json.encode(
                #     'utf-8') + b'\' > /vehicle_info_config.json\n')
                self.port.write(b'echo \'' + config_file_json.encode(
                    'utf-8') + b'\' > /emmc/svp/tpa/ota_carota/common4_de/bin/vehicle_info_config.json\n')
                return True
            # return False
            else:
                logger.error('串口没插')
        except AttributeError as e:
            logger.debug('串口没插')


    def reboot(self):
        cmd = "/emmc/svp/etc/reset-via-mcu.sh 30\n"
        logger.info('重启车机')
        self.port.write(cmd.encode('utf-8'))
        time.sleep(30)

    def ecu_uds_shell(self):
        pass

# serial_tool = SerialTool()

# serial_tool.reboot()
# serial_tool.set_file("vehicle_lock_door_status", 0)
# serial_tool.set_file("anti_theft_status", 0)
# serial_tool.set_file("power_gear_on", 0)
# serial_tool.set_file("gearbox_gear", 2)
# serial_tool.set_file("obd_status", 0)
# serial_tool.set_file("tbox_diag_status", 3)
# serial_tool.set_file("vehicle_model", 2)
# serial_tool.set_file("remote_poweroff", 1)
# serial_tool.set_file("remote_poweron", 1)
# serial_tool.set_file("otamodein", 0)
# serial_tool.set_file("ibs_soc", 1)
# serial_tool.set_file("otamodeout", 0)
# serial_tool.rm_package()

# serial_tool.set_file("otamodein",0,"otamodeout",0,"remote_poweroff",0,"remote_poweron",0)