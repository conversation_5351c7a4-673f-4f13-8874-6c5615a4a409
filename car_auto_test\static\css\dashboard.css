/* =============== 基础重置与全局样式 =============== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}



/* 下拉框容器 */
.multiselect-dropdown {
    position: relative;
    width: 300px;
}

/* 下拉按钮 */
.dropdown-button {
    width: 100%;
    min-height: 40px;
    padding: 5px 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: white;
    text-align: left;
    cursor: pointer;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 5px;
}

/* 下拉箭头图标 */
.arrow {
    transition: transform 0.3s;
    margin-left: auto;
}

.arrow.open {
    transform: rotate(180deg);
}

/* 标签选项容器 */
.dropdown-content {
    display: none;
    position: absolute;
    width: 100%;
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: white;
    z-index: 1000;
    margin-top: 5px;
}

/* 标签选项 */
.tag-option {
    padding: 8px 15px;
    cursor: pointer;
    display: flex;
    align-items: center;
}

.tag-option:hover {
    background-color: #f5f5f5;
}

/* 复选框样式 */
.tag-checkbox {
    margin-right: 10px;
}

/* 已选标签样式 - 在下拉按钮中显示 */
.selected-tag {
    background-color: #e0e0e0;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    display: flex;
    align-items: center;
}

/* 删除标签按钮 */
.remove-tag {
    margin-left: 5px;
    cursor: pointer;
    color: #666;
}

.remove-tag:hover {
    color: #333;
}

/* 占位文本 */
.placeholder {
    color: #999;
}


/* 系统级主题变量定义 */
:root {
    --primary-color: #1e3c72;      /* 主色调：深蓝色 */
    --danger-color: #dc3545;       /* 危险色：红色 */
    --success-color: #28a745;      /* 成功色：绿色 */
    --text-dark: #333;             /* 深色文本 */
    --text-light: #fff;            /* 浅色文本 */
    --border-color: #dee2e6;       /* 边框颜色 */
    --bg-light: #f8f9fa;           /* 浅色背景 */
    --shadow-sm: 0 2px 10px rgba(0,0,0,0.1);  /* 小阴影 */
    --shadow-md: 0 4px 15px rgba(0,0,0,0.1);  /* 中阴影 */
}

/* 基础页面样式 */
body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--bg-light);
    color: var(--text-dark);
    line-height: 1.6;
}

/* =============== 头部区域样式 =============== */
.header {
    background: linear-gradient(135deg, var(--primary-color) 0%, #2a5298 100%);
    color: var(--text-light);
    padding: 20px 0;
    box-shadow: var(--shadow-sm);
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Logo区域样式 */
.logo-section h1 {
    font-size: 2em;
    font-weight: 600;
    margin: 0;
}

.logo-section .subtitle {
    font-size: 0.9em;
    opacity: 0.9;
    margin-top: 5px;
}

/* 品牌信息样式 */
.brand-info {
    text-align: right;
    font-size: 0.9em;
}

.brand-logo {
    font-size: 1.5em;
    font-weight: bold;
    color: #ffd700;
    margin-bottom: 5px;
}

/* =============== 主容器样式 =============== */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* =============== 控制面板样式 =============== */
.control-panel {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow-sm);
}

.control-buttons {
    display: flex;
    gap: 15px;
    align-items: center;
}

/* 按钮基础样式 */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1em;
    font-weight: 500;
    transition: all 0.3s ease;
}

/* 主要按钮样式 */
.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.4);
}

/* 危险按钮样式 */
.btn-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #c82333 100%);
    color: white;
}

/* 禁用按钮样式 */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* =============== 状态指示器样式 =============== */
.status-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

/* 不同状态的指示点样式 */
.status-idle { background-color: #6c757d; }
.status-running {
    background-color: var(--success-color);
    animation: pulse 2s infinite;
}
.status-completed { background-color: #007bff; }
.status-failed { background-color: var(--danger-color); }

/* 状态点动画 */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* 车型选择器样式 */
.vehicle-selector {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
}

.vehicle-selector label {
    font-size: 0.9em;
    color: #495057;
    white-space: nowrap;
}

.form-select {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: white;
    color: #495057;
    font-size: 0.85em;
    cursor: pointer;
    transition: border-color 0.3s ease;
    width: 100%;
    box-sizing: border-box;
}

.form-select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-select:hover {
    border-color: #007bff;
}

.form-input {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: white;
    color: #495057;
    font-size: 0.85em;
    transition: border-color 0.3s ease;
    width: 100%;
    box-sizing: border-box;
}

.form-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-input:hover {
    border-color: #007bff;
}

/* =============== 主要内容区域布局 =============== */
.main-content {
    display: flex;
    gap: 20px;
}

/* 测试表格区域样式 */
.test-table-section {
    flex: 2;
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: var(--shadow-sm);
    height: fit-content;
}

/* 图片预览区域样式 */
.image-preview-section {
    flex: 1;
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: var(--shadow-sm);
    max-height: 800px;
    overflow-y: auto;
}

/* 区域标题样式 */
.section-title {
    font-size: 1.3em;
    font-weight: 600;
    margin-bottom: 20px;
    color: #2c3e50;
    border-bottom: 2px solid var(--border-color);
    padding-bottom: 10px;
}

/* =============== 表格系统样式 =============== */
.table-wrapper {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
}

.test-table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
}

/* 表格列宽设置 */
.test-table th:nth-child(1), .test-table td:nth-child(1) { width: 8%; }  /* 序号 */
.test-table th:nth-child(2), .test-table td:nth-child(2) { width: 12%; } /* 车型 */
.test-table th:nth-child(3), .test-table td:nth-child(3) { width: 12%; } /* 语言 */
.test-table th:nth-child(4), .test-table td:nth-child(4) { width: 12%; } /* 模式 */
.test-table th:nth-child(5), .test-table td:nth-child(5) { width: 30%; } /* 场景 */
.test-table th:nth-child(6), .test-table td:nth-child(6) { width: 16%; } /* 时间 */
.test-table th:nth-child(7), .test-table td:nth-child(7) { width: 10%; } /* 状态 */

/* 表头样式 */
.test-table th {
    background: var(--bg-light);
    font-weight: 600;
    color: #495057;
    padding: 12px 8px;
    text-align: center;
    border-bottom: 2px solid var(--border-color);
    position: relative;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    transition: background-color 0.2s;
}

.test-table th:hover {
    background: #e9ecef;
}

/* 可排序表头样式 */
.test-table th.sortable::after {
    content: '↑';
    font-size: 0.8em;
    margin-left: 5px;
    opacity: 0.5;
}

.test-table th.sort-asc::after {
    opacity: 1;
}

/* 表格单元格样式 */
.test-table td {
    padding: 10px 8px;
    text-align: center;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
    height: 50px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 场景列特殊样式 */
.test-table td:nth-child(5) {
    white-space: normal;
    word-wrap: break-word;
    word-break: break-all;
    line-height: 1.2;
    max-height: 50px;
    overflow: hidden;
    font-size: 0.9em;
    padding: 8px;
}

/* 表格行悬停效果 */
.test-table tbody tr:hover {
    background: var(--bg-light);
}

.test-table tbody tr.active {
    background: #e3f2fd;
    border-left: 4px solid #2196f3;
}

/* =============== 表格容器样式 =============== */
.table-container {
    border: 1px solid #eee;
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 0;
    /* height: 580px;  // 删除或注释掉 */
}
.table-body-container {
    max-height: 400px;
    overflow-y: auto;
    height: auto; /* 让高度自适应内容 */
    min-height: 0;
}
.pagination-container {
    margin-top: 0; /* 或者更小的值 */
    padding: 0;
    border-top: none;
}

/* =============== 分页系统样式 =============== */
.pagination-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 15px;
    padding: 10px 0;
    border-top: 1px solid var(--border-color);
}

.pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 20px;
    padding: 10px;
    background: white;
    border-radius: 8px;
    box-shadow: var(--shadow-sm);
}

.pagination button {
    padding: 6px 12px;
    border: 1px solid var(--border-color);
    background: white;
    color: #495057;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9em;
    transition: all 0.3s ease;
}

.pagination button:hover:not(:disabled) {
    background: #e9ecef;
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-info {
    font-size: 0.9em;
    color: #6c757d;
    margin: 0 15px;
}

/* =============== 图片预览样式 =============== */
.image-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    height: 500px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px dashed #dee2e6;
    overflow: hidden;
}

.preview-image {
    max-width: 100%;
    max-height: 400px;
    border-radius: 8px;
    box-shadow: var(--shadow-md);
    margin-bottom: 15px;
    display: block;
    margin-left: auto;
    margin-right: auto;
}

.image-container {
    width: 100%;
    text-align: center;
}

.image-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    border-radius: 5px;
    padding: 15px;
    margin-top: 10px;
}

.image-info {
    width: 100%;
    margin-bottom: 15px;
    text-align: left;
}

.image-info h4 {
    margin-bottom: 10px;
    color: #2c3e50;
}

.image-info p {
    margin: 5px 0;
    color: #6c757d;
}

/* =============== 通用样式类 =============== */
.no-image {
    color: #6c757d;
    font-style: italic;
    text-align: center;
    padding: 40px;
}

.badge {
    display: inline-block;
    padding: 0.25em 0.6em;
    font-size: 0.75em;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25rem;
}

.badge-primary { background-color: #007bff; color: white; }
.badge-success { background-color: var(--success-color); color: white; }
.badge-warning { background-color: #ffc107; color: #212529; }
.badge-danger { background-color: var(--danger-color); color: white; }

.refresh-info {
    font-size: 0.9em;
    color: #6c757d;
    margin-left: auto;
}

/* =============== 筛选下拉框样式 =============== */
.filter-dropdown {
    position: absolute;
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    box-shadow: var(--shadow-sm);
    z-index: 1000;
    min-width: 120px;
    max-height: 200px;
    overflow-y: auto;
    display: none;
}

.filter-dropdown.show {
    display: block;
}

.filter-item {
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.filter-item:hover {
    background-color: var(--bg-light);
}

.filter-item.active {
    background-color: #e3f2fd;
    color: #007bff;
}

.filterable {
    position: relative;
    cursor: pointer;
}

.filterable::after {
    content: '▼';
    font-size: 0.8em;
    margin-left: 5px;
    opacity: 0.5;
}

/* 导航按钮样式 */
.nav-buttons {
    position: fixed;
    top: 20px;
    right: 20px;
    display: flex;
    gap: 10px;
    z-index: 1000;
}

.nav-button {
    padding: 8px 16px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
}

.nav-button:hover {
    background-color: var(--primary-dark);
}

/* 确保按钮在导出页面中保持位置 */
.offline-page .nav-buttons {
    position: fixed;
    top: 20px;
    right: 20px;
    display: flex;
    gap: 10px;
    z-index: 1000;
}

/* 导出按钮样式 */
.export-button {
    padding: 8px 16px;
    background-color: var(--success-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
    margin-left: 10px;
}

.export-button:hover {
    background-color: var(--success-dark);
}

/* 图片模态框样式 */
.image-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    overflow: auto;
}

.modal-content {
    position: relative;
    margin: auto;
    padding: 0;
    width: 90%;
    height: 90%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.modal-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    margin: auto;
    display: block;
}

.close-modal {
    position: absolute;
    right: 25px;
    top: 10px;
    color: #f1f1f1;
    font-size: 35px;
    font-weight: bold;
    cursor: pointer;
    z-index: 1001;
}

.close-modal:hover,
.close-modal:focus {
    color: #bbb;
    text-decoration: none;
    cursor: pointer;
}

/* 预览图片样式优化 */
.preview-image {
    cursor: pointer;
    transition: transform 0.2s;
}

.preview-image:hover {
    transform: scale(1.02);
}



/* 多选下拉框样式 */
.multiselect-wrapper {
    position: relative;
    width: 100%;
    z-index: 1001;
}

.multiselect-trigger {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    padding: 7px 10px;
    background: #f9fafc;
    border: 1px solid #ddd;
    border-radius: 6px;
    position: relative;
    z-index: 1;
    min-height: 35px;
}

.multiselect-trigger .arrow {
    font-size: 10px;
    transition: transform 0.3s;
}

.multiselect-wrapper.open .multiselect-trigger .arrow {
    transform: rotate(180deg);
}

.multiselect-dropdown {
    display: none;
    position: fixed; /* 改为fixed定位，脱离所有容器限制 */
    width: 300px; /* 固定宽度，确保完整显示 */
    background: white;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 5px;
    z-index: 9999; /* 设置最高层级 */
    margin-top: 5px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
    max-height: 200px;
    overflow-y: auto;
}

.multiselect-wrapper.open .multiselect-dropdown {
    display: block;
}

.multiselect-option {
    padding: 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
}

.multiselect-option:hover {
    background: #f1f2f6;
}

.multiselect-option input {
    margin-right: 8px;
}

/* =============== 表单布局优化样式 =============== */
/* 表单组基础样式 */
.form-group {
    display: flex;
    flex-direction: column;
    flex: 0 0 auto;
    margin-bottom: 0;
    overflow: visible;
    margin-right: 15px; /* 增加基础元素间距 */
}

/* 表单组标签样式 */
.form-group label {
    font-size: 12px;
    margin-bottom: 4px;
    color: #555;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    text-align: center; /* 标签居中对齐 */
}

/* VIN码输入框样式 */
#vinInput {
    width: 100%;
    min-width: 100px;
    max-width: 120px;
}

/* 卡密输入框样式 */
#cardKeyInput {
    width: 100%;
    min-width: 80px;
    max-width: 100px;
}

/* 地区选择框样式 */
#vehicleSelect {
    width: 100%;
    min-width: 90px;
    max-width: 110px;
}

/* 车辆类型选择框样式 */
#vehicleType {
    width: 100%;
    min-width: 90px;
    max-width: 110px;
}

/* 语言选择框样式 - 保持原有功能 */
.form-group .multiselect-wrapper {
    width: 100%;
    min-width: 140px;
    max-width: 180px;
    position: relative;
    z-index: 1001;
}

/* 任务输入框样式 - 缩短宽度 */
#task1Input, #task2Input, #task3Input, #task4Input {
    width: 100%;
    min-width: 65px;  /* 缩短一半的宽度 */
    max-width: 75px;  /* 缩短一半的宽度 */
}

/* 任务输入框对应的表单组样式 - 使用类名替代:has()选择器 */
.task-form-group {
    min-width: 65px;
    max-width: 75px;
    margin-right: 25px !important; /* 保持任务框之间的间距 */
}

/* 控制行布局优化 */
.control-row.row-2 {
    display: flex;
    justify-content: flex-start;
    align-items: flex-end;
    gap: 0; /* 移除gap，使用margin-right控制间距 */
    flex-wrap: nowrap; /* 强制单行显示 */
    overflow-x: auto; /* 如果内容过宽，允许水平滚动 */
    overflow-y: visible; /* 允许下拉框向下展开 */
    padding: 15px;
    background: #f8fafd;
    border-radius: 8px;
    position: relative; /* 为下拉框提供定位上下文 */
}

/* 确保控制面板也允许下拉框展开 */
.control-panel {
    overflow: visible;
}

/* 确保所有表单元素在同一行 */
.control-row.row-2 .form-group {
    flex-shrink: 0; /* 防止元素被压缩 */
}

/* 响应式调整 - 在较小屏幕上允许换行 */
@media (max-width: 1200px) {
    .control-row.row-2 {
        flex-wrap: wrap;
        gap: 8px;
    }

    .form-group {
        margin-right: 0;
        margin-bottom: 10px;
    }
}