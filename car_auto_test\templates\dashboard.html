<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI测试管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }

        body {
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        /* 头部区域 - 缩小版 */
        .header {
            background: linear-gradient(135deg, #2c3e50, #1a2530);
            color: white;
            padding: 12px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            height: 80px;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .logo-section h1 {
            font-size: 22px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .subtitle {
            font-size: 12px;
            opacity: 0.8;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .page-switch {
            display: flex;
            gap: 8px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
        }

        .btn-outline-primary {
            background: transparent;
            border: 1px solid #4a9dff;
            color: #4a9dff;
        }

        .btn-outline-primary.active {
            background: #4a9dff;
            color: white;
        }

        .brand-info {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 14px;
        }

        .brand-logo {
            background: #4a9dff;
            color: white;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 16px;
        }

        /* 主容器 */
        .container {
            max-width: 1400px;
            margin: 20px auto;
            padding: 0 20px;
        }

        /* 控制面板 - 扩大版 */
        .control-panel {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            margin-bottom: 25px;
            overflow: visible;
        }

        .control-buttons {
            display: flex;
            flex-direction: column;
        }

        .btn-primary {
            background: #4a9dff;
            color: white;
        }

        .btn-danger {
            background: #ff4757;
            color: white;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        /* 表单样式已移至外部CSS文件 */

        .form-select, .form-input {
            padding: 7px 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: #f9fafc;
            font-size: 12px;
            width: 100%;
            box-sizing: border-box;
        }

        .form-select:focus, .form-input:focus {
            outline: none;
            border-color: #4a9dff;
            box-shadow: 0 0 0 3px rgba(74, 157, 255, 0.2);
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .status-idle {
            background: #a4b0be;
        }

        .status-running {
            background: #2ed573;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.4; }
            100% { opacity: 1; }
        }

        .refresh-info {
            background: #f1f2f6;
            padding: 8px 15px;
            border-radius: 6px;
            font-size: 13px;
            white-space: nowrap;
        }

        .control-row {
            display: flex;
            gap: 8px;
            width: 100%;
            margin-bottom: 0;
            flex-wrap: wrap;
            align-items: flex-end;
            justify-content: flex-start;
        }

        /* 优化后的布局样式 */
        .control-row.row-1 {
            background: #f8fafd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
        }

        .control-row.row-1 .form-group {
            min-width: 50px;
        }

        .status-refresh-group {
            display: flex;
            gap: 15px;
            align-items: center;
            margin-left: auto;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            margin-right: 20px;
        }

        /* control-row.row-2 样式已移至外部CSS文件 */

        .section-title {
            font-size: 18px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .section-title i {
            font-size: 20px;
            color: #4a9dff;
        }

        /* 主要内容区域 */
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
        }

        .test-table-section, .image-preview-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }

        .table-container {
            border: 1px solid #eee;
            border-radius: 6px;
            overflow: hidden;
            margin-bottom: 15px;
        }

        .table-wrapper {
            overflow-x: auto;
        }

        .test-table {
            width: 100%;
            border-collapse: collapse;
            min-width: 700px;
        }

        .test-table th {
            background: #f8f9fa;
            padding: 12px 15px;
            text-align: left;
            font-weight: 600;
            color: #495057;
            border-bottom: 2px solid #e9ecef;
        }

        .test-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #e9ecef;
            color: #495057;
        }

        .test-table tbody tr:hover {
            background: #f8f9fa;
        }

        .table-body-container {
            max-height: 400px;
            overflow-y: auto;
        }

        .no-image {
            text-align: center;
            padding: 40px 20px;
            color: #adb5bd;
        }

        .pagination-container {
            display: flex;
            justify-content: center;
        }

        .pagination {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .pagination button {
            padding: 8px 20px;
            background: #4a9dff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .image-preview {
            height: 500px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px dashed #dee2e6;
            overflow: hidden;
        }

        .image-preview img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .input-group {
            flex: 1;
        }

        .input-row {
            display: flex;
            gap: 15px;
            margin-top: 15px;
        }

        .input-row .form-group {
            flex: 1;
        }

        .status-section {
            display: flex;
            align-items: center;
            gap: 15px;
            background: #f8f9fa;
            padding: 10px 15px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .control-row {
                flex-wrap: wrap;
            }

            .control-row.row-2 .form-group {
                min-width: calc(50% - 10px);
                max-width: calc(50% - 10px);
                margin-bottom: 10px;
            }

            .status-refresh-group {
                margin-left: 0;
                width: 100%;
                justify-content: flex-end;
                margin-top: 15px;
            }
        }

        @media (max-width: 992px) {
            .control-row.row-2 .form-group {
                min-width: calc(33.33% - 14px);
            }
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                height: auto;
                padding: 10px;
            }

            .header {
                height: auto;
                padding: 10px 0;
            }

            .header-right {
                width: 100%;
                justify-content: space-between;
                margin-top: 10px;
            }

            .control-row.row-1 .form-group {
                min-width: 100%;
            }

            .control-row.row-2 .form-group {
                min-width: 100%;
                max-width: 100%;
                margin-bottom: 15px;
            }

            .action-buttons {
                width: 100%;
                justify-content: center;
                margin: 15px 0;
            }

            .status-refresh-group {
                flex-direction: column;
                gap: 10px;
                align-items: flex-start;
            }
        }

        /* VIN和卡密输入框样式 */
        .vin-card-group {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }
        /* 进度条相关样式已移除 */
    </style>
    <!-- 引入外部CSS文件 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
</head>
<body>
    <!-- 页面头部区域 -->
    <div class="header">
        <div class="header-content">
            <div class="logo-section">
                <h1>UI测试管理系统</h1>
                <div class="subtitle">UI Testing Management System</div>
            </div>
            <div class="header-right">
                <div class="page-switch">
{#                    <button id="switchToUI" class="btn btn-outline-primary active">UI测试</button>#}
{#                    <button id="switchToTask" class="btn btn-outline-primary">任务下发</button>#}
                </div>
                <div class="brand-info">
                    <div class="brand-logo">OTA</div>
                    <div>远程升级 · 品质生活</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容容器 -->
    <div class="container">
        <!-- 控制面板区域 -->
        <div class="control-panel">
            <div class="control-buttons">
                <!-- 第一行: 操作按钮和系统状态 -->
                <div class="control-row row-1">
                    <div class="action-buttons">
                        <button id="startTestBtn" class="btn btn-primary">
                            🚀 开始测试
                        </button>
                        <button id="stopTestBtn" class="btn btn-danger" disabled>
                            ⏹️ 停止测试
                        </button>
                    </div>

                    <!-- 进度条相关已注释
                    <div class="progress-wrapper">
                        <div class="progress-label" id="progressLabel">进度50%</div>
                        <div class="progress-container">
                            <div id="progressBar" class="progress-bar"></div>
                        </div>
                    </div>
                    -->
                    <div class="status-refresh-group">
                        <div class="status-indicator">
                            <div id="statusDot" class="status-dot status-idle"></div>
                            <span id="statusText">系统就绪</span>
                        </div>

                        <div class="refresh-info">
                            <span id="lastUpdate">最后更新: --</span>
                            <span style="margin-left: 10px;">自动刷新: 3秒</span>
                        </div>
                    </div>
                </div>
                <!-- 第二行: 所有输入框整合在同一行 -->
                <div class="control-row row-2" style="display: flex; gap: 15px; align-items: flex-end; flex-wrap: nowrap;">
                    <!-- VIN码 -->
                    <div class="form-group" style="min-width: 120px;">
                        <label for="vinInput">VIN码</label>
                        <input id="vinInput" type="text" class="form-input" placeholder="请输入VIN码">
                    </div>

                    <!-- 卡密 -->
                    <div class="form-group" style="min-width: 80px;">
                        <label for="cardKeyInput">卡密</label>
                        <input id="cardKeyInput" type="text" class="form-input" placeholder="请输入卡密">
                    </div>

                    <!-- 地区 -->
                    <div class="form-group" style="min-width: 80px;">
                        <label for="vehicleSelect">所属地区</label>
                        <select id="vehicleSelect" class="form-select">
                            <option value="EU">欧盟</option>
                            <option value="SA">沙特</option>
                            <option value="CA">中亚</option>
                            <option value="RU">俄罗斯</option>
                        </select>
                    </div>

                    <!-- 车辆类型 -->
                    <div class="form-group" style="min-width: 80px;">
                        <label for="vehicleType">车辆类型</label>
                        <select id="vehicleType" class="form-select">
                            <option value="PHEV">PHEV</option>
                            <option value="TANKER">油车</option>
                        </select>
                    </div>

                    <!-- 需截图语言 -->
                    <div class="form-group" style="min-width: 140px;">
                        <label for="languageSelect">需截图语言</label>
                        <div class="multiselect-wrapper">
                            <div class="multiselect-trigger form-select">
                                <span id="languagePlaceholder">请选择</span>
                                <span class="arrow">▼</span>
                            </div>
                            <div class="multiselect-dropdown">
                                <div class="multiselect-option">
                                    <input type="checkbox" id="lang_1" value="英语（英国）">
                                    <label for="lang_1">英语（英国）</label>
                                </div>
                                <div class="multiselect-option">
                                    <input type="checkbox" id="lang_30" value="中文（中国）">

                                    <label for="lang_30">中文（中国）</label>
                                </div>
                                <div class="multiselect-option">
                                    <input type="checkbox" id="lang_2" value="阿拉伯语（沙特阿拉伯）">
                                    <label for="lang_2">阿拉伯语（沙特阿拉伯）</label>
                                </div>
                                <div class="multiselect-option">
                                    <input type="checkbox" id="lang_3" value="西班牙语（西班牙）">
                                    <label for="lang_3">西班牙语（西班牙）</label>
                                </div>
                                <div class="multiselect-option">
                                    <input type="checkbox" id="lang_4" value="德语（德国）">
                                    <label for="lang_4">德语（德国）</label>
                                </div>
                                <div class="multiselect-option">
                                    <input type="checkbox" id="lang_5" value="法语（法国）">
                                    <label for="lang_5">法语（法国）</label>
                                </div>
                                <div class="multiselect-option">
                                    <input type="checkbox" id="lang_6" value="意大利语（意大利）">
                                    <label for="lang_6">意大利语（意大利）</label>
                                </div>
                                <div class="multiselect-option">
                                    <input type="checkbox" id="lang_7" value="荷兰语（荷兰）">
                                    <label for="lang_7">荷兰语（荷兰）</label>
                                </div>
                                <div class="multiselect-option">
                                    <input type="checkbox" id="lang_8" value="挪威尼诺斯克语（挪威）">
                                    <label for="lang_8">挪威尼诺斯克语（挪威）</label>
                                </div>
                                <div class="multiselect-option">
                                    <input type="checkbox" id="lang_9" value="挪威书面语（挪威）">
                                    <label for="lang_9">挪威书面语（挪威）</label>
                                </div>
                                <div class="multiselect-option">
                                    <input type="checkbox" id="lang_10" value="葡萄牙语（葡萄牙）">
                                    <label for="lang_10">葡萄牙语（葡萄牙）</label>
                                </div>
                                <div class="multiselect-option">
                                    <input type="checkbox" id="lang_11" value="瑞典语（瑞典）">
                                    <label for="lang_11">瑞典语（瑞典）</label>
                                </div>
                                <div class="multiselect-option">
                                    <input type="checkbox" id="lang_12" value="土耳其语（土耳其）">
                                    <label for="lang_12">土耳其语（土耳其）</label>
                                </div>
                                <div class="multiselect-option">
                                    <input type="checkbox" id="lang_13" value="俄语（俄罗斯）">
                                    <label for="lang_13">俄语（俄罗斯）</label>
                                </div>
                                <div class="multiselect-option">
                                    <input type="checkbox" id="lang_14" value="波兰语（波兰）">
                                    <label for="lang_14">波兰语（波兰）</label>
                                </div>
                                <div class="multiselect-option">
                                    <input type="checkbox" id="lang_15" value="波斯语（伊朗）">
                                    <label for="lang_15">波斯语（伊朗）</label>
                                </div>
                                <div class="multiselect-option">
                                    <input type="checkbox" id="lang_16" value="丹麦语（丹麦）">
                                    <label for="lang_16">丹麦语（丹麦）</label>
                                </div>
                                <div class="multiselect-option">
                                    <input type="checkbox" id="lang_17" value="希腊语（希腊）">
                                    <label for="lang_17">希腊语（希腊）</label>
                                </div>
                                <div class="multiselect-option">
                                    <input type="checkbox" id="lang_18" value="匈牙利语（匈牙利）">
                                    <label for="lang_18">匈牙利语（匈牙利）</label>
                                </div>
                                <div class="multiselect-option">
                                    <input type="checkbox" id="lang_19" value="罗马尼亚语（罗马尼亚）">
                                    <label for="lang_19">罗马尼亚语（罗马尼亚）</label>
                                </div>
                                <div class="multiselect-option">
                                    <input type="checkbox" id="lang_20" value="斯洛文尼亚语（塞拉利昂）">
                                    <label for="lang_20">斯洛文尼亚语（塞拉利昂）</label>
                                </div>
                                <div class="multiselect-option">
                                    <input type="checkbox" id="lang_21" value="克罗地亚语（克罗地亚）">
                                    <label for="lang_21">克罗地亚语（克罗地亚）</label>
                                </div>
                                <div class="multiselect-option">
                                    <input type="checkbox" id="lang_22" value="加泰罗尼亚语（西班牙）">
                                    <label for="lang_22">加泰罗尼亚语（西班牙）</label>
                                </div>
                                <div class="multiselect-option">
                                    <input type="checkbox" id="lang_23" value="捷克语（捷克）">
                                    <label for="lang_23">捷克语（捷克）</label>
                                </div>
                                <div class="multiselect-option">
                                    <input type="checkbox" id="lang_24" value="斯洛伐克语（斯洛伐克）">
                                    <label for="lang_24">斯洛伐克语（斯洛伐克）</label>
                                </div>
                                <div class="multiselect-option">
                                    <input type="checkbox" id="lang_25" value="立陶宛语（立陶宛）">
                                    <label for="lang_25">立陶宛语（立陶宛）</label>
                                </div>
                                <div class="multiselect-option">
                                    <input type="checkbox" id="lang_26" value="拉脱维亚语（拉脱维亚）">
                                    <label for="lang_26">拉脱维亚语（拉脱维亚）</label>
                                </div>
                                <div class="multiselect-option">
                                    <input type="checkbox" id="lang_27" value="芬兰语（芬兰）">
                                    <label for="lang_27">芬兰语（芬兰）</label>
                                </div>
                                <div class="multiselect-option">
                                    <input type="checkbox" id="lang_28" value="爱沙尼亚语（爱沙尼亚）">
                                    <label for="lang_28">爱沙尼亚语（爱沙尼亚）</label>
                                </div>
                                <div class="multiselect-option">
                                    <input type="checkbox" id="lang_29" value="保加利亚语（保加利亚）">
                                    <label for="lang_29">保加利亚语（保加利亚）</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 任务输入框 -->
                    <div class="form-group task-form-group">
                        <label for="task1Input">正常升级</label>
                        <input id="task1Input" type="text" class="form-input" placeholder="任务编号">
                    </div>
                    <div class="form-group task-form-group">
                        <label for="task2Input">正常降级</label>
                        <input id="task2Input" type="text" class="form-input" placeholder="任务编号">
                    </div>
                    <div class="form-group task-form-group">
                        <label for="task3Input">不可预约升级</label>
                        <input id="task3Input" type="text" class="form-input" placeholder="任务编号">
                    </div>
                    <div class="form-group task-form-group">
                        <label for="task4Input">不可预约降级</label>
                        <input id="task4Input" type="text" class="form-input" placeholder="任务编号">
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 测试数据表格区域 -->
            <div class="test-table-section">
                <h2 class="section-title">📊 测试执行记录</h2>

                <!-- 表格容器 -->
                <div class="table-container">
                    <!-- 表头固定区域 -->
                    <div class="table-wrapper">
                        <table class="test-table">
                            <thead>
                                <tr>
                                    <th class="sortable" data-sort="id">序号</th>
                                    <th>车型</th>
                                    <th class="filterable" data-filter="language">语言</th>
                                    <th class="filterable" data-filter="mode">模式</th>
                                    <th>场景</th>
                                    <th>截图时间</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                        </table>
                    </div>

                    <!-- 表格主体滚动区域 -->
                    <div class="table-body-container">
                        <table class="test-table">
                            <tbody id="testTableBody">
                                <tr>
                                    <td colspan="7" class="no-image">暂无测试数据，请点击"开始测试"按钮开始测试</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 分页控件区域 -->
                <div class="pagination-container">
                    <div class="pagination" id="pagination">
                        <button id="prevPage">上一页</button>
                        <div class="pagination-info">
                            <span id="pageInfo">第 1 页，共 1 页</span>
                            <span id="totalInfo">共 0 条记录</span>
                        </div>
                        <button id="nextPage">下一页</button>
                    </div>
                </div>
            </div>

            <!-- 图片预览区域 -->
            <div class="image-preview-section">
                <h2 class="section-title">🖼️ 实时截图预览</h2>
                <div id="imagePreview" class="image-preview">
                    <div class="no-image">
                        <div style="font-size: 3em; margin-bottom: 15px;">📷</div>
                        <p>暂无截图预览</p>
                        <p>测试开始后，最新的截图将在此处显示</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入外部JavaScript文件 -->
    <script src="{{ url_for('static', filename='js/dashboard.js') }}"></script>

</body>
</html>