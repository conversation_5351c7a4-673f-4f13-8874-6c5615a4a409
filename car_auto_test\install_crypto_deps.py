#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装加密依赖脚本
"""

import subprocess
import sys

def install_dependencies():
    """安装加密所需的依赖"""
    
    dependencies = [
        'cryptography',
        'pyinstaller',
    ]
    
    print("📦 正在安装加密依赖...")
    
    for dep in dependencies:
        try:
            print(f"安装 {dep}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', dep])
            print(f"✅ {dep} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ {dep} 安装失败: {e}")
            return False
    
    print("🎉 所有依赖安装完成!")
    return True

if __name__ == "__main__":
    install_dependencies()
