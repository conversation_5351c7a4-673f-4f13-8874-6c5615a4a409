"""
项目入口文件
"""
from flask import Flask, render_template, jsonify, request, send_from_directory, abort
from flask import Flask
from config.api_config import APIConfig
from api.task_api import task_bp
from api.ui_screenshot_api  import screenshot_bp
from api.start_test_api  import start_test_bp
from api.report_api import report_bp

def create_app():
    app = Flask(__name__)
    # 加载配置
    app.config.from_object(APIConfig)

    # 注册蓝图
    app.register_blueprint(task_bp)
    app.register_blueprint(screenshot_bp)
    app.register_blueprint(start_test_bp)
    app.register_blueprint(report_bp)

    @app.route('/')
    def index():
        """主页面"""
        return render_template('dashboard.html')

    @app.route('/task_dispatch')
    def task_dispatch():
        """任务下发页面"""
        return render_template('task_dispatch.html')

    return app

#
# if __name__ == '__main__':
#     app = create_app()
#
#     # 配置日志
#     import logging
#     log = logging.getLogger('werkzeug')
#     log.setLevel(logging.INFO)
#     # 启动应用
#     app.run(
#         host=APIConfig.HOST,   # 关闭调试模式，防止自动重启
#         port=APIConfig.PORT,
#         debug=APIConfig.DEBUG,
#         threaded=APIConfig.THREADED,
#         use_reloader=False  # 禁用自动重载
#     )
