# -*- mode: python ; coding: utf-8 -*-

# 导入加密模块
from PyInstaller.utils.crypt import PyiBlockCipher

# 设置加密密钥（请更改为您自己的密钥）
ENCRYPTION_KEY = 'your_secret_key_here_change_this_to_your_own_key'
block_cipher = PyiBlockCipher(aes_key=ENCRYPTION_KEY.encode())

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('resources.bundle', '.'),  # 只包含加密的资源包
        ('resource_manager.py', '.'),  # 包含资源管理器
    ],
    hiddenimports=[
        'uiautomator2',
        'packaging', 
        'resource_manager',
        'cryptography',
        'cryptography.fernet',
        'cryptography.hazmat.primitives',
        'cryptography.hazmat.primitives.kdf.pbkdf2',
        'cryptography.hazmat.primitives.hashes'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(
    a.pure,
    a.zipped_data,
    cipher=block_cipher,
)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='ota_ui',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='ota.ico',
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='main',
)
