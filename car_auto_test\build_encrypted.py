#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
加密构建脚本 - 用于创建加密的应用程序包
"""

import os
import sys
import shutil
import tempfile
import subprocess
from resource_manager import ResourceManager

def create_encrypted_spec():
    """创建使用加密资源的spec文件"""
    
    # 1. 创建加密资源包
    print("🔐 正在创建加密资源包...")
    
    rm = ResourceManager()
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 复制需要加密的资源到临时目录
        resources_to_encrypt = [
            'templates',
            'static', 
            'config',
            'utils',
            'api',
            'testcases',
            'core'
        ]
        
        for resource in resources_to_encrypt:
            if os.path.exists(resource):
                if os.path.isdir(resource):
                    shutil.copytree(resource, os.path.join(temp_dir, resource))
                else:
                    shutil.copy2(resource, temp_dir)
        
        # 复制单个文件
        single_files = ['pytest.ini', 'conftest.py']
        for file in single_files:
            if os.path.exists(file):
                shutil.copy2(file, temp_dir)
        
        # 创建加密包
        bundle_path = 'resources.bundle'
        if rm.create_encrypted_bundle(temp_dir, bundle_path):
            print(f"✅ 加密资源包创建成功: {bundle_path}")
            return True
        else:
            print("❌ 加密资源包创建失败")
            return False
            
    finally:
        # 清理临时目录
        shutil.rmtree(temp_dir)

def create_encrypted_spec_file():
    """创建加密版本的spec文件"""
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

# 导入加密模块
from PyInstaller.utils.crypt import PyiBlockCipher

# 设置加密密钥（请更改为您自己的密钥）
ENCRYPTION_KEY = 'your_secret_key_here_change_this_to_your_own_key'
block_cipher = PyiBlockCipher(aes_key=ENCRYPTION_KEY.encode())

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('resources.bundle', '.'),  # 只包含加密的资源包
        ('resource_manager.py', '.'),  # 包含资源管理器
    ],
    hiddenimports=[
        'uiautomator2',
        'packaging', 
        'resource_manager',
        'cryptography',
        'cryptography.fernet',
        'cryptography.hazmat.primitives',
        'cryptography.hazmat.primitives.kdf.pbkdf2',
        'cryptography.hazmat.primitives.hashes'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(
    a.pure,
    a.zipped_data,
    cipher=block_cipher,
)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='ota_ui',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='ota.ico',
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='main',
)
'''
    
    with open('main_encrypted.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 创建加密spec文件: main_encrypted.spec")

def modify_main_py():
    """修改main.py以使用加密资源"""
    
    # 备份原始文件
    if os.path.exists('main.py') and not os.path.exists('main_original.py'):
        shutil.copy2('main.py', 'main_original.py')
        print("📋 备份原始main.py为main_original.py")
    
    # 读取原始main.py
    with open('main.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 在文件开头添加资源管理器导入
    import_code = '''
# 导入资源管理器（用于加密资源）
try:
    from resource_manager import get_resource_path, cleanup_resources
    import atexit
    atexit.register(cleanup_resources)  # 程序退出时清理临时文件
    ENCRYPTED_MODE = True
except ImportError:
    # 开发模式回退
    def get_resource_path(path):
        return path
    ENCRYPTED_MODE = False

'''
    
    # 如果还没有添加过，则添加导入代码
    if 'from resource_manager import' not in content:
        content = import_code + content
    
    # 替换模板和静态文件路径
    replacements = [
        ("app = Flask(__name__)", 
         "app = Flask(__name__, template_folder=get_resource_path('templates'), static_folder=get_resource_path('static'))"),
        ("'templates'", "get_resource_path('templates')"),
        ("'static'", "get_resource_path('static')"),
        ("'config'", "get_resource_path('config')"),
    ]
    
    for old, new in replacements:
        if old in content and new not in content:
            content = content.replace(old, new)
    
    # 保存修改后的文件
    with open('main.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 修改main.py以支持加密资源")

def build_encrypted():
    """构建加密版本"""
    
    print("🚀 开始构建加密版本...")
    
    # 1. 创建加密资源包
    if not create_encrypted_spec():
        print("❌ 创建加密资源包失败")
        return False
    
    # 2. 创建加密spec文件
    create_encrypted_spec_file()
    
    # 3. 修改main.py
    modify_main_py()
    
    # 4. 运行PyInstaller
    print("📦 正在运行PyInstaller...")
    try:
        result = subprocess.run([
            sys.executable, '-m', 'PyInstaller', 
            'main_encrypted.spec', 
            '--clean'
        ], check=True, capture_output=True, text=True)
        
        print("✅ 构建成功!")
        print(f"输出目录: dist/main/")
        
        # 清理构建文件
        cleanup_build_files()
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def cleanup_build_files():
    """清理构建过程中的临时文件"""
    files_to_remove = [
        'resources.bundle',
        'main_encrypted.spec',
        'build'
    ]
    
    for item in files_to_remove:
        if os.path.exists(item):
            if os.path.isdir(item):
                shutil.rmtree(item)
            else:
                os.remove(item)
            print(f"🧹 清理: {item}")

def restore_original():
    """恢复原始文件"""
    if os.path.exists('main_original.py'):
        shutil.move('main_original.py', 'main.py')
        print("🔄 恢复原始main.py")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='加密构建工具')
    parser.add_argument('--build', action='store_true', help='构建加密版本')
    parser.add_argument('--restore', action='store_true', help='恢复原始文件')
    parser.add_argument('--clean', action='store_true', help='清理构建文件')
    
    args = parser.parse_args()
    
    if args.restore:
        restore_original()
    elif args.clean:
        cleanup_build_files()
    elif args.build:
        success = build_encrypted()
        if success:
            print("\n🎉 加密构建完成!")
            print("📁 可执行文件位于: dist/main/ota_ui.exe")
            print("🔐 资源文件已加密，无法直接查看")
        else:
            print("\n💥 构建失败，请检查错误信息")
    else:
        print("使用方法:")
        print("  构建加密版本: python build_encrypted.py --build")
        print("  恢复原始文件: python build_encrypted.py --restore") 
        print("  清理构建文件: python build_encrypted.py --clean")
