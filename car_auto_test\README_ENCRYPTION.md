# PyInstaller 资源文件加密方案

本方案提供了多种方法来隐藏或加密PyInstaller打包后的资源文件，防止明文显示在dist文件夹中。

## 🔐 方案概述

### 方案1：PyInstaller内置加密（简单）
- 使用PyInstaller的内置AES加密
- 加密Python字节码和部分资源
- 配置简单，但保护程度有限

### 方案2：自定义资源加密（推荐）
- 将所有资源文件打包成加密的bundle
- 运行时动态解密到临时目录
- 提供更强的保护

### 方案3：混合加密（最强）
- 结合方案1和方案2
- 双重加密保护

## 📋 使用步骤

### 1. 安装依赖
```bash
python install_crypto_deps.py
```

### 2. 构建加密版本
```bash
python build_encrypted.py --build
```

### 3. 清理构建文件（可选）
```bash
python build_encrypted.py --clean
```

### 4. 恢复原始文件（如需要）
```bash
python build_encrypted.py --restore
```

## 🛠️ 文件说明

### 核心文件
- `resource_manager.py` - 资源管理器，处理加密/解密
- `build_encrypted.py` - 自动化构建脚本
- `main_encrypted.spec` - 加密版本的spec文件（自动生成）

### 工具文件
- `install_crypto_deps.py` - 安装加密依赖
- `README_ENCRYPTION.md` - 本说明文档

## 🔧 手动配置

### 修改现有spec文件
如果您想手动配置现有的spec文件，可以参考以下步骤：

1. **添加加密设置**：
```python
from PyInstaller.utils.crypt import PyiBlockCipher

ENCRYPTION_KEY = 'your_secret_key_here'
block_cipher = PyiBlockCipher(aes_key=ENCRYPTION_KEY.encode())
```

2. **修改Analysis配置**：
```python
a = Analysis(
    ['main.py'],
    # ... 其他配置
    cipher=block_cipher,  # 添加加密
    hiddenimports=['cryptography'],  # 添加加密库
)
```

3. **修改PYZ配置**：
```python
pyz = PYZ(
    a.pure,
    a.zipped_data,
    cipher=block_cipher,  # 添加加密
)
```

### 使用资源管理器
在您的主程序中：

```python
from resource_manager import get_resource_path

# 替换原来的路径
app = Flask(__name__, 
           template_folder=get_resource_path('templates'),
           static_folder=get_resource_path('static'))
```

## 🔒 安全特性

### 加密保护
- **AES-256加密**：使用工业级加密算法
- **密钥派生**：使用PBKDF2从密码派生密钥
- **随机盐值**：增强密钥安全性

### 运行时保护
- **临时解密**：资源只在内存和临时目录中解密
- **自动清理**：程序退出时自动清理临时文件
- **错误处理**：解密失败时的优雅降级

## 📊 保护级别对比

| 方案 | 保护级别 | 性能影响 | 配置复杂度 |
|------|----------|----------|------------|
| 原始打包 | ⭐ | 无 | 简单 |
| 内置加密 | ⭐⭐ | 轻微 | 简单 |
| 自定义加密 | ⭐⭐⭐⭐ | 中等 | 中等 |
| 混合加密 | ⭐⭐⭐⭐⭐ | 较高 | 复杂 |

## ⚠️ 注意事项

### 性能考虑
- 首次启动时需要解压资源，可能稍慢
- 建议对大文件进行压缩后再加密
- 临时文件会占用磁盘空间

### 安全考虑
- 请修改默认的加密密钥
- 不要在代码中硬编码敏感信息
- 考虑使用代码混淆进一步保护

### 兼容性
- 支持Windows、Linux、macOS
- 需要Python 3.6+
- 需要足够的临时目录空间

## 🐛 故障排除

### 常见问题

1. **ImportError: No module named 'cryptography'**
   ```bash
   pip install cryptography
   ```

2. **解密失败**
   - 检查加密密钥是否正确
   - 确保资源包完整

3. **临时文件清理失败**
   - 检查磁盘空间
   - 确保有写入权限

4. **打包失败**
   - 检查PyInstaller版本
   - 确保所有依赖已安装

### 调试模式
在resource_manager.py中启用调试：
```python
# 在ResourceManager类中添加
self.debug = True  # 启用调试输出
```

## 📞 技术支持

如果遇到问题，请检查：
1. Python版本兼容性
2. 依赖库版本
3. 磁盘空间和权限
4. 防病毒软件干扰

## 🔄 版本历史

- v1.0 - 基础加密功能
- v1.1 - 添加自动化构建脚本
- v1.2 - 改进错误处理和清理机制
