import hashlib
import hmac
import datetime
import os
import requests
import sys
import email.utils


SALT = b"zyhzwj_secret_salt_2025"

# 本地保存上次校验通过的时间戳
LAST_TIME_FILE = "utils/CloudOperation/last_time.txt"

def get_network_time():
    """从百度获取当前UTC时间戳，防止本地时间被篡改"""
    try:
        resp = requests.head("https://www.baidu.com", timeout=5)
        date_str = resp.headers['Date']  # 格式如: 'Tue, 16 Jul 2024 09:12:34 GMT'
        dt = email.utils.parsedate_to_datetime(date_str)
        return int(dt.timestamp())
    except Exception as e:
        print(f"无法获取网络时间: {e}")
        sys.exit(1)

def check_time_rollback(current_time):
    """防止日期回滚"""
    if os.path.exists(LAST_TIME_FILE):
        with open(LAST_TIME_FILE, "r") as f:
            last_time = float(f.read().strip())
        if current_time < last_time:
            print("检测到系统时间回退，程序终止！")
            sys.exit(1)
        # 可选：检测时间跳跃
        if current_time > last_time + 86400 * 3:
            print("警告：检测到系统时间大幅跳跃！")
    # 更新本地时间戳
    with open(LAST_TIME_FILE, "w") as f:
        f.write(str(current_time))

def generate_code(timestamp):
    """根据UTC日期和盐生成6位数字密钥"""
    date_str = datetime.datetime.utcfromtimestamp(timestamp).strftime("%Y%m%d")
    h = hmac.new(SALT, date_str.encode() + b"zyhzwj", hashlib.sha256).digest()
    return f"{int.from_bytes(h[:3], 'big') % 1000000:06d}"


# 1. 获取网络时间
current_time = get_network_time()
# 2. 防回滚
check_time_rollback(current_time)
# 3. 生成今日密钥

code = generate_code(current_time)




