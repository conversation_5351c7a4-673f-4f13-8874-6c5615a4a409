elements = {

    # 隐私申明
    "engineering_privacy_btn": "com.carota.chery:id/rl_protocol",

    # 隐私申明确定按钮
    "engineering_privacy_acknowledge_btn": "com.mega.carsettings:id/tv_ok",

    "engineering_privacy_acknowledge_btn1": "com.desaysv.launcher:id/tv_acknowledge",

    # 当前系统已是最新版本
    "engineering_mode": "com.carota.chery:id/entry_txt_title_second",
    # 密码输入框
    "engineering_password": "com.carota.chery:id/pw_edit",
    # 密码确定按钮点击
    "password_confirm": "com.carota.chery:id/pw_btn_confirm",
    # 检查更新
    "check_update": "com.carota.chery:id/engine_btn_check",
    # 清除数据
    "clear_data": "com.carota.chery:id/engine_btn_recovery",
    # 返回按钮
    "back_button": "com.carota.chery:id/detail_iv_back",
    # 发现新版本标题
    "find_new_version": "com.carota.chery:id/tv_title",
    # 发现新版本ico
    "notify_img": "com.carota.chery:id/notify_img",
    # 发现新版本ico
    "notify_img_update": "com.carota.chery:id/notify_img_update",
    # T26 车速不过 确定按钮
    "update_conditions_not_meet_btn_class": "android.widget.LinearLayout",
    # T22下电失败 确定按钮
    "t22_btn": "com.carota.chery:id/ele_btn_confirm",
    # 立即升级按钮
    "now_btn": "com.carota.chery:id/ea_btn_update",
    # 任务无效按钮
    "invalid_btn_confirm": "com.carota.chery:id/invalid_btn_confirm",
    # 预约升级按钮
    "schedule_btn": "com.carota.chery:id/ea_btn_schedule",
    # 详情标题
    "details_title": "com.carota.chery:id/strategy_txt_title",
    # 详情主页面
    "details_home": "com.carota.chery:id/coll_tv_detail",
    # 详情返回按钮
    "details_back_btn": "com.carota.chery:id/detail_iv_back",
    # 主页面车辆升级
    "ota": "",
    # 注意事项继续按钮
    "matter_argee_btn": "android.widget.Button",  # 索引0
    # 注意事项取消按钮/相同格式取消按钮均可用此元素
    "matter_cancel_btn": "android.widget.Button",  # 索引1
    # 注意事项弹框
    "matter_cancel_frame": "com.carota.chery:id/tv_title",
    # 免责同意按钮
    "disclaimer_argee_btn": "com.carota.chery:id/btn_agree",
    # 免责取消按钮
    "disclaimer_cancel_btn": "取消",
    # 免责页面弹框
    "disclaimer_cancel_frame": "com.carota.chery:id/disclaimer_txt_title",
    # 电源处于OFF挡位
    "gear_off": "android.widget.LinearLayout",  # 索引0
    # 请将车辆熄火取消按钮
    "vehicle_acc_btn": "com.carota.chery:id/acc_btn_cancel",
    # 请将车辆熄火页面
    "vehicle_acc_frame": "com.carota.chery:id/acc_img_icon",
    # 第一次升级准备中
    "update_preparing": "com.carota.chery:id/acc_tv_title",
    # T26 desay 第一次升级准备中
    "update_preparing_desay": "com.carota.chery:id/tv_title",

    # 升级条件不满足
    "update_conditions_not_meet": "com.carota.chery:id/tv_title",
    # 升级条件不满足
    "update_conditions_not_meet_btn": "com.carota.chery:id/ele_btn_confirm",
    # 蓄电池电量不满足
    "ibs_soc_fail_btn": "com.carota.chery:id/incomplete_btn_confirm",
    # 上电失败确定
    "remote_power_on_fail_btn": "com.carota.chery:id/ele_btn_confirm",
    # 上高压确定
    "hvo_on_fail_btn": "com.carota.chery:id/ota_btn_confirm",
    # 下电失败确定
    "remote_power_off_fail_btn": "com.carota.chery:id/error_btn_confirm",
    # T26 德赛下电失败确定
    "remote_power_off_fail_desay_btn": "com.carota.chery:id/ele_btn_confirm",

    # 远程上电
    "power_on": "com.carota.chery:id/tv_on",
    # 进ota模式失败确定/上高压失败
    "otamodein_fail_btn": "com.carota.chery:id/ota_btn_confirm",
    "otamodein_fail_btn2": "com.carota.chery:id/ea_btn_update",


    # 退ota模式失败
    "otamodeout_fail_btn": "com.carota.chery:id/error_btn_confirm",
    "desay_otamodeout_fail_btn": "com.carota.chery:id/ota_btn_confirm",
    # 前置条件标题
    "precondition": "com.carota.chery:id/precondition_tv_title",
    # 前置不满足
    "precondition_fail": "com.carota.chery:id/pcd_text",
    # 前置不满足取消按钮
    "precondition_fail_cancelbtn": "com.carota.chery:id/pcd_btn_cancel",

    # 前置不满足确定按钮
    "precondition_fail_confirbtn": "com.carota.chery:id/pcd_btn_confirm",
    # 前置全部通过
    "precondition_pass": "com.carota.chery:id/pcd_text",
    # 更新进行中
    "update_title": "com.carota.chery:id/ins_txt_title",
    # 更新提示文本
    "update_text": "",
    # 升级成功文本
    "success_text": "com.carota.chery:id/por_tv_title",
    # 升级成功按钮
    "success_btn": "com.carota.chery:id/btn_confirm",
    # 升级成功、退出ota失败
    "success_otamode_fail": "com.carota.chery:id/ota_tv_title",
    # 升级成功、下电失败
    "success_poweroff_fail": "com.carota.chery:id/por_tv_title",
    # 任务无效页面
    "invalid_task": "com.carota.chery:id/invalid_img_icon",
    # 任务无效确认按钮
    "invalid_task_confirm_btn": "com.carota.chery:id/invalid_btn_confirm",
    # 预约设置页面标题
    "schedule_time_set_title": "com.carota.chery:id/schedule_tv_title",
    # 预约设置页面确定按钮/重新预约按钮
    "schedule_time_set_agree_btn": "com.carota.chery:id/schedule_btn_check",
    # 预约设置成功弹框icon
    "schedule_time_set_success_icon": "com.carota.chery:id/schedule_img_icon",
    # 预约设置成功弹框确定按钮
    "schedule_time_set_success_btn": "com.carota.chery:id/schedule_btn_confirm",
    # 取消预约按钮
    "schedule_cancel_btn": "com.carota.chery:id/ea_btn_cancel_schedule",
    # 预约前置不满足弹标题
    "schedule_predition_fail_title": "com.carota.chery:id/fire_tv_title",
    # 预约前置不满足弹框确定按钮
    "schedule_predition_fail_btn": "com.carota.chery:id/fire_btn_confirm",
    # 预约时间到红绿灯界面
    "tomorrow_time_come": "com.carota.chery:id/fire_tv_title",
    # 预约时间到红绿灯界面,取消按钮
    "tomorrow_time_come_back_btn": "com.carota.chery:id/fire_btn_cancel",
    # 通知中心
    "notifications": "",
    "control_center": "",
    'system': 'android.widget.LinearLayout',
    # 车辆设置-显示菜单栏
    'display': 'com.desaysv.setting:id/img_menu',
    # 白天黑夜按钮
    'night_day': 'android.widget.RadioButton',  # 索引 1: 白天   2：黑夜
    # 工程模式多语言按钮
    "Multilingual_btn": "com.carota.chery:id/engine_multi_language"

}
