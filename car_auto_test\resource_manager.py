#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资源管理器 - 用于加密/解密和管理应用程序资源文件
"""

import os
import sys
import base64
import tempfile
import shutil
from cryptography.fernet import Fe<PERSON>t
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import zipfile
import json

class ResourceManager:
    """资源管理器类"""
    
    def __init__(self, password="your_app_secret_key"):
        """初始化资源管理器"""
        self.password = password.encode()
        self.key = self._generate_key()
        self.cipher = Fernet(self.key)
        self.temp_dir = None
        
    def _generate_key(self):
        """从密码生成加密密钥"""
        salt = b'stable_salt_for_app'  # 固定盐值，确保密钥一致
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(self.password))
        return key
    
    def encrypt_file(self, file_path):
        """加密单个文件"""
        try:
            with open(file_path, 'rb') as f:
                data = f.read()
            
            encrypted_data = self.cipher.encrypt(data)
            
            # 保存加密文件
            encrypted_path = file_path + '.enc'
            with open(encrypted_path, 'wb') as f:
                f.write(encrypted_data)
            
            return encrypted_path
        except Exception as e:
            print(f"加密文件失败 {file_path}: {e}")
            return None
    
    def decrypt_file(self, encrypted_path, output_path=None):
        """解密单个文件"""
        try:
            with open(encrypted_path, 'rb') as f:
                encrypted_data = f.read()
            
            decrypted_data = self.cipher.decrypt(encrypted_data)
            
            if output_path is None:
                output_path = encrypted_path.replace('.enc', '')
            
            with open(output_path, 'wb') as f:
                f.write(decrypted_data)
            
            return output_path
        except Exception as e:
            print(f"解密文件失败 {encrypted_path}: {e}")
            return None
    
    def create_encrypted_bundle(self, source_dir, bundle_path):
        """创建加密的资源包"""
        try:
            # 创建临时zip文件
            temp_zip = tempfile.mktemp(suffix='.zip')
            
            with zipfile.ZipFile(temp_zip, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, dirs, files in os.walk(source_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arc_path = os.path.relpath(file_path, source_dir)
                        zipf.write(file_path, arc_path)
            
            # 加密zip文件
            with open(temp_zip, 'rb') as f:
                zip_data = f.read()
            
            encrypted_data = self.cipher.encrypt(zip_data)
            
            # 保存加密包
            with open(bundle_path, 'wb') as f:
                f.write(encrypted_data)
            
            # 清理临时文件
            os.remove(temp_zip)
            
            print(f"创建加密资源包: {bundle_path}")
            return True
            
        except Exception as e:
            print(f"创建加密包失败: {e}")
            return False
    
    def extract_encrypted_bundle(self, bundle_path, extract_to=None):
        """解压加密的资源包"""
        try:
            if extract_to is None:
                extract_to = tempfile.mkdtemp()
            
            # 解密包
            with open(bundle_path, 'rb') as f:
                encrypted_data = f.read()
            
            zip_data = self.cipher.decrypt(encrypted_data)
            
            # 创建临时zip文件
            temp_zip = tempfile.mktemp(suffix='.zip')
            with open(temp_zip, 'wb') as f:
                f.write(zip_data)
            
            # 解压到目标目录
            with zipfile.ZipFile(temp_zip, 'r') as zipf:
                zipf.extractall(extract_to)
            
            # 清理临时文件
            os.remove(temp_zip)
            
            self.temp_dir = extract_to
            return extract_to
            
        except Exception as e:
            print(f"解压加密包失败: {e}")
            return None
    
    def get_resource_path(self, relative_path):
        """获取资源文件路径（支持打包后的环境）"""
        if getattr(sys, 'frozen', False):
            # 打包后的环境
            if self.temp_dir is None:
                # 尝试解压内置的加密资源包
                bundle_path = os.path.join(sys._MEIPASS, 'resources.bundle')
                if os.path.exists(bundle_path):
                    self.extract_encrypted_bundle(bundle_path)
            
            if self.temp_dir:
                return os.path.join(self.temp_dir, relative_path)
            else:
                # 回退到默认路径
                return os.path.join(sys._MEIPASS, relative_path)
        else:
            # 开发环境
            return os.path.join(os.path.dirname(__file__), relative_path)
    
    def cleanup(self):
        """清理临时文件"""
        if self.temp_dir and os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
            self.temp_dir = None

# 全局资源管理器实例
resource_manager = ResourceManager()

def get_resource_path(relative_path):
    """便捷函数：获取资源路径"""
    return resource_manager.get_resource_path(relative_path)

def cleanup_resources():
    """便捷函数：清理资源"""
    resource_manager.cleanup()

if __name__ == "__main__":
    # 测试和工具功能
    import argparse
    
    parser = argparse.ArgumentParser(description='资源管理器工具')
    parser.add_argument('--encrypt-dir', help='加密目录到资源包')
    parser.add_argument('--output', help='输出文件路径')
    parser.add_argument('--decrypt-bundle', help='解密资源包')
    parser.add_argument('--extract-to', help='解压到指定目录')
    
    args = parser.parse_args()
    
    rm = ResourceManager()
    
    if args.encrypt_dir:
        output = args.output or 'resources.bundle'
        if rm.create_encrypted_bundle(args.encrypt_dir, output):
            print(f"✅ 成功创建加密资源包: {output}")
        else:
            print("❌ 创建加密资源包失败")
    
    elif args.decrypt_bundle:
        extract_to = args.extract_to or './extracted'
        result = rm.extract_encrypted_bundle(args.decrypt_bundle, extract_to)
        if result:
            print(f"✅ 成功解压到: {result}")
        else:
            print("❌ 解压失败")
    
    else:
        print("使用示例:")
        print("  加密目录: python resource_manager.py --encrypt-dir ./static --output resources.bundle")
        print("  解密包:   python resource_manager.py --decrypt-bundle resources.bundle --extract-to ./temp")
