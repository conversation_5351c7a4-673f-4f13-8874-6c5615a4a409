'''通用配置文件'''
import os

# 通用

# 自动化用例执行逻辑与ui截图自动化逻辑切换 0代表自动化用例，1代表自动化截图
SWITCH = 1

# 云端配置
# CHERY_URL = "https://backend-ota-uat.mychery.com"    # 国内测试环境、账号、VIN
# CHERY_USER = "zhengjianyang"
# CHERY_PASSWORD = "QWASZx123!"
# VIN = 'LVUGFB227SD831668'

CHERY_URL = "https://backend-ota-dev.cheryinternational.com"    # 国际测试环境、账号、VIN
CHERY_USER = "Automation"
CHERY_PASSWORD = "Auto_test123!"
VIN = 'LVVGTBCD2RD903203'
PLATFORM = "奇瑞欧盟"


# 日志存放路径
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
LOG_PATH = os.path.join(BASE_DIR, r'\testreport\Logs')
SQLITE_PATH = BASE_DIR + r"\utils\DbManage\DB"
OTALOG_PATH = BASE_DIR + r"\testreport\Otalog"
SCREENSHOT_PATH = BASE_DIR + r"\testreport\screenshot"
ZLGLOG_PATH = BASE_DIR + r"\testreport\zlglog"

# 车机类型标志位 1, 德赛 2,  伯泰克镁佳 3,  东软 4, 讯飞
TYPE_POSITION = None


'''
----------------------------------------------------------------------------------------------------------------
'''

# 4.0
# VIN = 'LVVDD21B0PC001990'

EE4_VIN = 'LVVGTBCD2RD903203'
# EEA4_UI_VIN ='LVTDT22B0ND220125'
# EEA4_UI_VIN ='LNNACDEE7SD139647'
# EEA4_UI_VIN ='LVVGTBCD2RD903203'

# 任务检测最大等待时间
EEA4_max_wait_time = 400  # 10分钟
# 任务检测10秒循环一次
EEA4_interval = 2  # 每10秒检查一次

# 任务配置
normal_task = ["t1gc-ice-csa-0100-0101", "t1gc-ice-csa-0101-0100"]
abnormal_task = ["T1EJ-ICE-DMC-0304"]

# 语言列表
language_list = ['chinese','english']
# 白天黑夜
day_night_list =['day','night']
# 不同模式文件夹存放
# mode_0 = BASE_DIR +r"\utils\ImageRecognition\Image_ui"
# mode_1 = BASE_DIR +r"\utils\ImageRecognition\Image_ui\chinese_day"
# mode_2 = BASE_DIR +r"\utils\ImageRecognition\Image_ui\chinese_night"
# mode_3 = BASE_DIR +r"\utils\ImageRecognition\Image_ui\english_day"
# mode_4 = BASE_DIR +r"\utils\ImageRecognition\Image_ui\english_night"

# 4.0车型信息
FOUR_APP_PACKAGE = "com.carota.chery"
FOUR_APP_PACKAGE_ACTIVITY = '.ui.EntryActivity'

# 任务检测最大等待时间
max_wait_time = 230  # 10分钟
# 任务检测10秒循环一次
interval = 2  # 每10秒检查一次

x_image = BASE_DIR + r"\utils\ImageRecognition\Image\x_mark.jpg"
d_image = BASE_DIR + r"\utils\ImageRecognition\Image\d_mark.jpg"
loading_image = BASE_DIR + r"\utils\ImageRecognition\Image\jiazai_mark.jpg"
save_dir= BASE_DIR + r"\utils\ImageRecognition\Image"
# ui截图路径
ui_dir= BASE_DIR + r"\utils\ImageRecognition\Image_ui"

# 串口配置
vehicle_path = '/emmc/svp/tpa/ota_carota/common4_de/bin'
setup_cmd = '. /emmc/svp/etc/svp.env.setup.sh'
define_rate = [921600, 115200]



# 配置文件json
config_file={
        "remote_poweron":0,
        "remote_poweroff":0,
        "otamodein":0,
        "otamodeout":0,
        # "hvodown":0,
        # "hvoup":0,

    "obd_status":0,
"act_vehicleinfo":1
}
# #
# apk
eea4_apk_config_file={
	# "test_bench": 1,
	# "vcu_power_hook": 0,
	# "timeout": 900,
	# "timeout_exit_ota": 1,
	# "ota_off": 0,
	# "remote_power_on": 0,
	# "remote_power_off": 0,
	# "hv_in": 0,
	# "hv_out": 0,
	# "power_state": 1,
	# "gear_state": 0,
	# "charge_state": 0,
	# "speed": 0,
	# "battery_voltage": 13330,
	# "battery_power": 90,
	# "battery_level": 90,
	# # "handbrake_state": 1,
	# "engine_state": 1,
	# "motor_state": 1,
	# "diagnose_state": 0,
	"tel_diagnose_state": 0,
	# "vehicle_mode_state": 2,
	# "lock_state": 0
	# "window_state": 0,
	# "security_state": 0,
	# "hv_ready_state": 0,
	# "vtol_state": 0,
	# "pet_mode": 0,
	# "sentinel_mode": 0,
	# "dcdc_mode": 0,
	# "engine_speed": 2000,
	# "scene_mode": 0,
	# "driver_seat_belt": 0
}

eea4_qnx_config_file={
	# "remote_poweron": 0,
	# "remote_poweroff": 0,
	# "otamodein": 0,
	# "otamodeout": 0,
	# "hvodown": 0,
	# "power_gear": 0,
	"ibs_soc": 99,
	"battery_soc": 99,
	# # "engine_status": 0,
	# "vehicle_speed": 0,
	# "gearbox_gear": 2,   # 挡位  2是D
	# "epb_status": 1,   # 手刹 1是拉起
	# "obd_status": 0,
	# "anti_theft_status": 0,
	# # "vehicle_model": 2,
	"tbox_diag_status": 3,
	# "vehicle_charge_status": 0,
	# "vehicle_lock_door_status": 0,
	# "hv_ready": 0,
	# "pv_ready": 0,
	# "battery_voltage": 0,
	# "hvo_status": 0,
	# "pwron_status": 0,
    # "pwrdown_status": 0,
	# "engine_speed": 0,
	# "obc_chg_sts": 1,
	# "steering_wheel": 0,
	# "ecall_status": 0,
	# "pt_ready": 0,
	# "hvoup": 0,
	# "hvoon_ready": 0,
	# "hvooff_ready":0,
    # "anti_theft_status":0,
	"act_vehicleinfo":1
}

'''
----------------------------------------------------------------------------------------------------------------
'''

# 5.0车型信息
FIVE_APP_PACKAGE = "com.carota.chery.v5"
# FIVE_APP_PACKAGE_ACTIVITY = 'com.carota.lib.executor.ui.activity.mainActivity.MainActivity'


# 任务检测最大等待时间
EEA5_max_wait_time = 720  # 10分钟
# 任务检测10秒循环一次
EEA5_interval = 10  # 每10秒检查一次

# EEA5_VIN = 'LNNBDDDS7RD000461'
# EEA5_VIN = 'LNNBBDEM0RD470093'
EEA5_VIN = 'LNNAJDDS3RD000414'
# EEA5_VIN = 'LNNAJDDS1PDA34061'

eea5_config_file={
    "remote_poweron": 0,
    "remote_poweroff": 0,
    "otamodein": 0,
    "otamodeout": 0,
    "otastatus": 0,
    "hv_off": 0,
    "hv_on": 0,
    "power_mode": 2,
    "gear": 1, #挡位 1通过
    "master_vehicle_batt": 90,  # 蓄电池电量
    "hv_battle_soc": 81, # 动力电池电量
    "epb_status": 1,  # 手刹 1通过
"pwron_status": 2,
    "vehicle_speed": 0,  # 车速
    "pet_mode": 0,  # 童虫模式
    "ibs_soc": 0,
    "master_vehicle_soc": 0,
    "slave_vehicle_batt": 0,
    "slave_vehicle_soc": 0,
    "engine_status": 0,
    "car_model": 2,
    "tbox_diag_status": 0,

#     # "sentinel_mode": -1,
#     # "alrm_status": -1,
#     # "anti_theft_status": -1,
    "vehicle_charge_leg_status": 0, # 放电
#     "obd_status": 4,  # obd
#     "obc_chg_sts":4,

    # "vehicle_lock_door_fl_status": 1,
    # "vehicle_lock_door_fr_status": 1,
    # "vehicle_lock_door_rl_status": 1,
    # "vehicle_lock_door_rr_status": 1,
    "hv_ready": 0,
    "pt_ready": 0,

}